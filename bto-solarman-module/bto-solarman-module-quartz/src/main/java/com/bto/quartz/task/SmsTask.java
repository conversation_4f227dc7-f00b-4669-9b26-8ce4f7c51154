package com.bto.quartz.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.IotCardQuery;
import com.bto.commons.pojo.dto.SmsInfoDTO;
import com.bto.commons.pojo.entity.ClientInfo;
import com.bto.commons.pojo.vo.IotCardInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.TokenHolder;
import com.bto.redis.utils.RedisUtil;
import com.bto.sms.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.bto.commons.constant.OauthConstants.*;
import static com.bto.commons.constant.TimingTimeConstant.*;

/**
 * 短信发送定时任务
 * 定时检查物联网卡流量使用情况，对即将到期的卡片发送预警短信
 * 支持批量短信发送和分批次处理，确保系统性能和稳定性
 * 
 * <AUTHOR>
 * @date 2023/11/10 17:52
 */
@Slf4j
@Service
public class SmsTask {

    @Autowired
    private SmsService smsService;
    @Autowired
    private DeviceServiceClient deviceServiceClient;
    @Autowired
    private SystemServiceClient systemServiceClient;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 执行短信预警任务
     * 定期检查物联网卡流量使用情况，对剩余流量即将到期的卡片发送预警短信
     * 支持分批次处理，避免大批量短信发送造成的系统压力
     *
     * @param params 任务执行参数，可用于指定检查周期或其他配置信息
     */
    public void run(String params) {
        log.info("开始执行短信预警任务，参数：{}", params);

        try {
            // 获取访问令牌
            String accessToken = getAccessToken();
            if (accessToken == null) {
                log.error("获取访问令牌失败，任务终止");
                throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED);
            }

            // 查询物联网卡信息
            List<IotCardInfoVO> iotCardList = getIotCardList(accessToken);
            if (iotCardList == null || iotCardList.isEmpty()) {
                log.info("未查询到需要发送短信的物联网卡信息");
                return;
            }

            // 筛选符合条件的物联网卡
            List<IotCardInfoVO> filterList = filterIotCards(iotCardList);
            if (filterList.isEmpty()) {
                log.info("筛选后无符合条件的物联网卡，任务结束");
                return;
            }

            log.info("筛选出{}张符合条件的物联网卡", filterList.size());

            // 发送短信
            sendSmsInBatches(filterList);

            log.info("短信预警任务执行完成，共处理{}张卡片", filterList.size());

        } catch (Exception e) {
            log.error("短信预警任务执行失败", e);
            throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED);
        }
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    private String getAccessToken() {
        try {
            TokenHolder.setTokenValidationFlag(false);
            String accessToken = (String) redisUtil.get("bto_admin");

            if (accessToken == null) {
                log.info("Redis中未找到访问令牌，重新获取");
                ClientInfo client = new ClientInfo();
                client.setClientId("admin");
                client.setClientSecret("$2a$10$4XzCJPuzfBQHNocchVztDe2WY0Wy7V0EFoEC8.pQBYC0rBCEMuhva");
                client.setScope("all");
                client.setGrantType(CLIENT_CREDENTIALS);

                Object userLoginInfo = systemServiceClient.getAccessToken(client);
                Result userResult = JSONObject.parseObject(JSON.toJSONString(userLoginInfo), Result.class);

                if (userResult != null && userResult.getData() != null) {
                    accessToken = (String) BeanUtil.beanToMap(userResult.getData()).get(TOKEN_OF_ACCESS);
                    log.info("成功获取新的访问令牌");
                } else {
                    log.error("获取访问令牌失败，系统服务返回结果为空");
                    return null;
                }
            }

            return accessToken;
        } catch (Exception e) {
            log.error("获取访问令牌时发生异常", e);
            return null;
        }
    }

    /**
     * 查询物联网卡信息列表
     *
     * @param accessToken 访问令牌
     * @return 物联网卡信息列表
     */
    private List<IotCardInfoVO> getIotCardList(String accessToken) {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                log.info("第{}次尝试查询物联网卡信息", retryCount + 1);

                IotCardQuery query = new IotCardQuery();
                query.setPageSize(-1);
                query.setCurrentPage(-1);
                query.setRemainingDays(String.valueOf(THIRTY_DAYS));

                Result result = deviceServiceClient.getIotCardByPage(query, TOKEN_TYPE_BEARER + " " + accessToken);

                if (result != null && result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
                    TokenHolder.setTokenValidationFlag(true);
                    IPage page = JSONObject.parseObject(JSONObject.toJSONString(result.getData()), Page.class);
                    String jsonString = JSONObject.toJSONString(page.getRecords());
                    List<IotCardInfoVO> iotCardList = JSONUtil.toList(jsonString, IotCardInfoVO.class);

                    log.info("成功查询到{}张物联网卡信息", iotCardList.size());
                    return iotCardList;
                } else {
                    log.warn("查询物联网卡信息失败，返回结果：{}", result);
                }

            } catch (Exception e) {
                log.error("第{}次查询物联网卡信息时发生异常", retryCount + 1, e);

                // 如果是超时异常，等待一段时间后重试
                if (e.getCause() instanceof java.util.concurrent.TimeoutException ||
                    e.getMessage().contains("timeout") ||
                    e.getMessage().contains("timed-out")) {

                    try {
                        Thread.sleep(5000 * (retryCount + 1)); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            retryCount++;
        }

        log.error("查询物联网卡信息失败，已重试{}次", maxRetries);
        return null;
    }

    /**
     * 筛选符合条件的物联网卡
     *
     * @param iotCardList 物联网卡列表
     * @return 筛选后的物联网卡列表
     */
    private List<IotCardInfoVO> filterIotCards(List<IotCardInfoVO> iotCardList) {
        return iotCardList.stream().filter(
                iotCardInfoVO -> {
                    Integer remainingDays = iotCardInfoVO.getRemainingDays();
                    return remainingDays != null && (
                            remainingDays.equals(ZERO_DAY) ||
                            remainingDays.equals(SEVEN_DAYS) ||
                            remainingDays.equals(FIFTEEN_DAYS) ||
                            remainingDays.equals(THIRTY_DAYS)
                    );
                }
        ).collect(Collectors.toList());
    }

    /**
     * 分批发送短信
     *
     * @param filterList 筛选后的物联网卡列表
     */
    private void sendSmsInBatches(List<IotCardInfoVO> filterList) {
        // 提取电话号码并转化成号码列表
        List<String> phoneNumbers = filterList.stream()
                .map(IotCardInfoVO::getPhoneNumber)
                .filter(phone -> phone != null && !phone.trim().isEmpty())
                .collect(Collectors.toList());

        List<SmsInfoDTO> smsInfoList = BeanUtil.copyToList(filterList, SmsInfoDTO.class);

        // 分批处理，每批100条
        List<List<String>> splitPhones = ListUtil.split(phoneNumbers, 100);
        List<List<SmsInfoDTO>> splitSmsInfo = ListUtil.split(smsInfoList, 100);

        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < splitPhones.size(); i++) {
            try {
                log.info("开始发送第{}批短信，共{}条", i + 1, splitPhones.get(i).size());
                smsService.batchSendAlarmMsg(splitPhones.get(i), splitSmsInfo.get(i));
                successCount += splitPhones.get(i).size();
                log.info("第{}批短信发送成功", i + 1);

                // 批次间添加延迟，避免过于频繁的请求
                if (i < splitPhones.size() - 1) {
                    try {
                        Thread.sleep(1000); // 1秒延迟
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("批次间延迟被中断");
                    }
                }

            } catch (ExecutionException e) {
                failCount += splitPhones.get(i).size();
                log.error("第{}批短信发送失败 - ExecutionException", i + 1, e);
            } catch (InterruptedException e) {
                failCount += splitPhones.get(i).size();
                log.error("第{}批短信发送失败 - InterruptedException", i + 1, e);
                Thread.currentThread().interrupt();
                break; // 中断异常时停止后续批次处理
            } catch (Exception e) {
                failCount += splitPhones.get(i).size();
                log.error("第{}批短信发送失败 - 未知异常", i + 1, e);
            }
        }

        log.info("短信发送完成，成功：{}条，失败：{}条", successCount, failCount);

        // 如果有失败的短信，记录但不抛出异常，避免影响定时任务的正常执行
        if (failCount > 0) {
            log.warn("有{}条短信发送失败，请检查短信服务状态", failCount);
        }
    }


}
