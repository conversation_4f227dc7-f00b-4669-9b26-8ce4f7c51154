package com.bto.quartz.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.api.feign.system.SystemServiceClient;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.IotCardQuery;
import com.bto.commons.pojo.dto.SmsInfoDTO;
import com.bto.commons.pojo.entity.ClientInfo;
import com.bto.commons.pojo.vo.IotCardInfoVO;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.commons.utils.TokenHolder;
import com.bto.redis.utils.RedisUtil;
import com.bto.sms.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.bto.commons.constant.OauthConstants.*;
import static com.bto.commons.constant.TimingTimeConstant.*;

/**
 * 短信发送定时任务
 * 定时检查物联网卡流量使用情况，对即将到期的卡片发送预警短信
 * 支持批量短信发送和分批次处理，确保系统性能和稳定性
 * 
 * <AUTHOR>
 * @date 2023/11/10 17:52
 */
@Slf4j
@Service
public class SmsTask {

    @Autowired
    private SmsService smsService;
    @Autowired
    private DeviceServiceClient deviceServiceClient;
    @Autowired
    private SystemServiceClient systemServiceClient;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 执行短信预警任务
     * 定期检查物联网卡流量使用情况，对剩余流量即将到期的卡片发送预警短信
     * 支持分批次处理，避免大批量短信发送造成的系统压力
     *
     * @param params 任务执行参数，可用于指定检查周期或其他配置信息
     */
    public void run(String params) {
        log.info("开始执行短信预警任务，参数：{}", params);
        log.info("任务执行时间：{}", new java.util.Date());

        // 测试短信服务连接
        testSmsService();

        try {
            // 获取访问令牌
            String accessToken = getAccessToken();
            if (accessToken == null) {
                log.error("获取访问令牌失败，任务终止");
                throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED);
            }

            // 查询物联网卡信息
            List<IotCardInfoVO> iotCardList = getIotCardList(accessToken);
            if (iotCardList == null || iotCardList.isEmpty()) {
                log.warn("未查询到任何物联网卡信息，可能原因：");
                log.warn("1. 数据库中没有物联网卡数据");
                log.warn("2. 查询条件过于严格（剩余天数小于{}天）", THIRTY_DAYS);
                log.warn("3. 设备服务返回空结果");
                return;
            }

            log.info("查询到物联网卡总数：{}", iotCardList.size());
            log.debug("原始查询结果详情：");
            for (int i = 0; i < Math.min(iotCardList.size(), 10); i++) {
                IotCardInfoVO card = iotCardList.get(i);
                log.debug("卡片[{}]: CIMI={}, 电话={}, 剩余天数={}, 电站={}",
                    i + 1, card.getCimi(), card.getPhoneNumber(),
                    card.getRemainingDays(), card.getPlantName());
            }
            if (iotCardList.size() > 10) {
                log.debug("... 还有{}张卡片未显示", iotCardList.size() - 10);
            }

            // 筛选符合条件的物联网卡
            List<IotCardInfoVO> filterList = filterIotCards(iotCardList);
            if (filterList.isEmpty()) {
                log.warn("筛选后无符合条件的物联网卡，任务结束");
                log.warn("筛选条件：剩余天数为 {} 或 {} 或 {} 或 {} 天",
                    ZERO_DAY, SEVEN_DAYS, FIFTEEN_DAYS, THIRTY_DAYS);
                log.warn("请检查数据库中是否有符合条件的物联网卡");
                return;
            }

            log.info("筛选出{}张符合条件的物联网卡", filterList.size());
            log.info("符合条件的卡片详情：");
            for (IotCardInfoVO card : filterList) {
                log.info("卡片: CIMI={}, 电话={}, 剩余天数={}, 用户={}, 电站={}",
                    card.getCimi(), card.getPhoneNumber(), card.getRemainingDays(),
                    card.getUserName(), card.getPlantName());
            }

            // 发送短信
            sendSmsInBatches(filterList);

            log.info("短信预警任务执行完成，共处理{}张卡片", filterList.size());

        } catch (Exception e) {
            log.error("短信预警任务执行失败", e);
            throw new BusinessException(ResultEnum.SCHEDULE_JOB_FAILED);
        }
    }

    /**
     * 获取访问令牌
     *
     * @return 访问令牌
     */
    private String getAccessToken() {
        try {
            TokenHolder.setTokenValidationFlag(false);
            String accessToken = (String) redisUtil.get("bto_admin");

            if (accessToken == null) {
                log.info("Redis中未找到访问令牌，重新获取");
                ClientInfo client = new ClientInfo();
                client.setClientId("admin");
                client.setClientSecret("$2a$10$4XzCJPuzfBQHNocchVztDe2WY0Wy7V0EFoEC8.pQBYC0rBCEMuhva");
                client.setScope("all");
                client.setGrantType(CLIENT_CREDENTIALS);

                Object userLoginInfo = systemServiceClient.getAccessToken(client);
                Result userResult = JSONObject.parseObject(JSON.toJSONString(userLoginInfo), Result.class);

                if (userResult != null && userResult.getData() != null) {
                    accessToken = (String) BeanUtil.beanToMap(userResult.getData()).get(TOKEN_OF_ACCESS);
                    log.info("成功获取新的访问令牌");
                } else {
                    log.error("获取访问令牌失败，系统服务返回结果为空");
                    return null;
                }
            }

            return accessToken;
        } catch (Exception e) {
            log.error("获取访问令牌时发生异常", e);
            return null;
        }
    }

    /**
     * 查询物联网卡信息列表
     *
     * @param accessToken 访问令牌
     * @return 物联网卡信息列表
     */
    private List<IotCardInfoVO> getIotCardList(String accessToken) {
        int maxRetries = 3;
        int retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                log.info("第{}次尝试查询物联网卡信息", retryCount + 1);

                IotCardQuery query = new IotCardQuery();
                query.setPageSize(-1);
                query.setCurrentPage(-1);
                query.setRemainingDays(String.valueOf(THIRTY_DAYS));

                Result result = deviceServiceClient.getIotCardByPage(query, TOKEN_TYPE_BEARER + " " + accessToken);

                if (result != null && result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
                    TokenHolder.setTokenValidationFlag(true);
                    IPage page = JSONObject.parseObject(JSONObject.toJSONString(result.getData()), Page.class);
                    String jsonString = JSONObject.toJSONString(page.getRecords());
                    List<IotCardInfoVO> iotCardList = JSONUtil.toList(jsonString, IotCardInfoVO.class);

                    log.info("成功查询到{}张物联网卡信息", iotCardList.size());
                    log.debug("查询参数: 剩余天数小于等于{}天", THIRTY_DAYS);
                    log.debug("返回数据示例（前3条）:");
                    for (int i = 0; i < Math.min(iotCardList.size(), 3); i++) {
                        IotCardInfoVO card = iotCardList.get(i);
                        log.debug("  [{}] CIMI: {}, 电话: {}, 剩余天数: {}, 状态: {}",
                            i + 1, card.getCimi(), card.getPhoneNumber(),
                            card.getRemainingDays(), card.getCardStatus());
                    }
                    return iotCardList;
                } else {
                    log.warn("查询物联网卡信息失败，返回结果：{}", result);
                    if (result != null) {
                        log.warn("失败详情 - 状态码: {}, 消息: {}", result.getStatus(), result.getMessage());
                    } else {
                        log.warn("设备服务返回null结果");
                    }
                }

            } catch (Exception e) {
                log.error("第{}次查询物联网卡信息时发生异常", retryCount + 1, e);

                // 如果是超时异常，等待一段时间后重试
                if (e.getCause() instanceof java.util.concurrent.TimeoutException ||
                    e.getMessage().contains("timeout") ||
                    e.getMessage().contains("timed-out")) {

                    try {
                        Thread.sleep(5000 * (retryCount + 1)); // 递增等待时间
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }

            retryCount++;
        }

        log.error("查询物联网卡信息失败，已重试{}次", maxRetries);
        return null;
    }

    /**
     * 筛选符合条件的物联网卡
     *
     * @param iotCardList 物联网卡列表
     * @return 筛选后的物联网卡列表
     */
    private List<IotCardInfoVO> filterIotCards(List<IotCardInfoVO> iotCardList) {
        log.info("开始筛选物联网卡，原始数据{}张", iotCardList.size());
        log.info("筛选条件：剩余天数为 {} 或 {} 或 {} 或 {} 天",
            ZERO_DAY, SEVEN_DAYS, FIFTEEN_DAYS, THIRTY_DAYS);

        // 统计各种剩余天数的卡片数量
        Map<Integer, Long> dayCountMap = iotCardList.stream()
            .filter(card -> card.getRemainingDays() != null)
            .collect(Collectors.groupingBy(IotCardInfoVO::getRemainingDays, Collectors.counting()));

        log.info("剩余天数分布统计：");
        dayCountMap.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> log.info("  剩余{}天: {}张卡片", entry.getKey(), entry.getValue()));

        // 统计null值
        long nullCount = iotCardList.stream()
            .filter(card -> card.getRemainingDays() == null)
            .count();
        if (nullCount > 0) {
            log.warn("发现{}张卡片的剩余天数为null，将被过滤", nullCount);
        }

        List<IotCardInfoVO> filteredList = iotCardList.stream().filter(
                iotCardInfoVO -> {
                    Integer remainingDays = iotCardInfoVO.getRemainingDays();
                    boolean matches = remainingDays != null && (
                            remainingDays.equals(ZERO_DAY) ||
                            remainingDays.equals(SEVEN_DAYS) ||
                            remainingDays.equals(FIFTEEN_DAYS) ||
                            remainingDays.equals(THIRTY_DAYS)
                    );

                    if (matches) {
                        log.debug("符合条件的卡片: CIMI={}, 剩余天数={}, 电话={}",
                            iotCardInfoVO.getCimi(), remainingDays, iotCardInfoVO.getPhoneNumber());
                    }

                    return matches;
                }
        ).collect(Collectors.toList());

        log.info("筛选完成，符合条件的卡片{}张", filteredList.size());

        // 按剩余天数分组显示筛选结果
        Map<Integer, Long> filteredDayCountMap = filteredList.stream()
            .collect(Collectors.groupingBy(IotCardInfoVO::getRemainingDays, Collectors.counting()));

        log.info("筛选后的剩余天数分布：");
        filteredDayCountMap.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry -> log.info("  剩余{}天: {}张卡片", entry.getKey(), entry.getValue()));

        return filteredList;
    }

    /**
     * 分批发送短信
     *
     * @param filterList 筛选后的物联网卡列表
     */
    private void sendSmsInBatches(List<IotCardInfoVO> filterList) {
        log.info("开始准备短信发送，待处理卡片数量：{}", filterList.size());

        // 提取电话号码并转化成号码列表
        List<String> allPhoneNumbers = filterList.stream()
                .map(IotCardInfoVO::getPhoneNumber)
                .collect(Collectors.toList());

        log.info("提取到的电话号码总数：{}", allPhoneNumbers.size());

        // 过滤空电话号码
        List<String> phoneNumbers = allPhoneNumbers.stream()
                .filter(phone -> phone != null && !phone.trim().isEmpty())
                .collect(Collectors.toList());

        log.info("有效电话号码数量：{}", phoneNumbers.size());

        // 统计无效电话号码
        long invalidPhoneCount = allPhoneNumbers.size() - phoneNumbers.size();
        if (invalidPhoneCount > 0) {
            log.warn("发现{}个无效电话号码（null或空字符串）", invalidPhoneCount);
            // 打印无效电话号码对应的卡片信息
            filterList.stream()
                .filter(card -> card.getPhoneNumber() == null || card.getPhoneNumber().trim().isEmpty())
                .forEach(card -> log.warn("无效电话号码的卡片: CIMI={}, 用户={}, 电站={}",
                    card.getCimi(), card.getUserName(), card.getPlantName()));
        }

        if (phoneNumbers.isEmpty()) {
            log.error("没有有效的电话号码，无法发送短信");
            return;
        }

        List<SmsInfoDTO> smsInfoList = BeanUtil.copyToList(filterList, SmsInfoDTO.class);
        log.info("转换短信信息DTO数量：{}", smsInfoList.size());

        // 打印短信信息详情
        log.info("短信信息详情（前5条）：");
        for (int i = 0; i < Math.min(smsInfoList.size(), 5); i++) {
            SmsInfoDTO smsInfo = smsInfoList.get(i);
            log.info("  [{}] 电话: {}, CIMI: {}, 剩余天数: {}",
                i + 1, smsInfo.getPhoneNumber(), smsInfo.getCimi(), smsInfo.getRemainingDays());
        }

        // 分批处理，每批100条
        List<List<String>> splitPhones = ListUtil.split(phoneNumbers, 100);
        List<List<SmsInfoDTO>> splitSmsInfo = ListUtil.split(smsInfoList, 100);

        log.info("分批处理：共{}批，每批最多100条", splitPhones.size());

        int successCount = 0;
        int failCount = 0;

        for (int i = 0; i < splitPhones.size(); i++) {
            List<String> currentBatchPhones = splitPhones.get(i);
            List<SmsInfoDTO> currentBatchSmsInfo = splitSmsInfo.get(i);

            log.info("=== 开始发送第{}批短信 ===", i + 1);
            log.info("批次信息：第{}/{}批，本批数量：{}", i + 1, splitPhones.size(), currentBatchPhones.size());

            // 打印本批次的电话号码
            log.info("本批次电话号码列表：{}", currentBatchPhones);

            // 打印本批次的短信详细信息
            log.info("本批次短信详细信息：");
            for (int j = 0; j < currentBatchSmsInfo.size(); j++) {
                SmsInfoDTO smsInfo = currentBatchSmsInfo.get(j);
                log.info("  [{}] 电话: {}, CIMI: {}, 剩余天数: {}天, 用户: {}, 电站: {}",
                    j + 1, smsInfo.getPhoneNumber(), smsInfo.getCimi(),
                    smsInfo.getRemainingDays(), smsInfo.getUserName(), smsInfo.getPlantName());
            }

            try {
                log.info("调用短信服务发送短信...");
                long startTime = System.currentTimeMillis();

                smsService.batchSendAlarmMsg(currentBatchPhones, currentBatchSmsInfo);

                long endTime = System.currentTimeMillis();
                successCount += currentBatchPhones.size();

                log.info("第{}批短信发送成功！耗时：{}ms", i + 1, endTime - startTime);
                log.info("成功发送{}条短信", currentBatchPhones.size());

                // 批次间添加延迟，避免过于频繁的请求
                if (i < splitPhones.size() - 1) {
                    log.info("等待1秒后发送下一批...");
                    try {
                        Thread.sleep(1000); // 1秒延迟
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.warn("批次间延迟被中断");
                    }
                }

            } catch (ExecutionException e) {
                failCount += currentBatchPhones.size();
                log.error("第{}批短信发送失败 - ExecutionException", i + 1, e);
                log.error("失败的电话号码：{}", currentBatchPhones);
            } catch (InterruptedException e) {
                failCount += currentBatchPhones.size();
                log.error("第{}批短信发送失败 - InterruptedException", i + 1, e);
                log.error("失败的电话号码：{}", currentBatchPhones);
                Thread.currentThread().interrupt();
                break; // 中断异常时停止后续批次处理
            } catch (Exception e) {
                failCount += currentBatchPhones.size();
                log.error("第{}批短信发送失败 - 未知异常", i + 1, e);
                log.error("失败的电话号码：{}", currentBatchPhones);
                log.error("异常详情：{}", e.getMessage());
            }

            log.info("=== 第{}批短信处理完成 ===", i + 1);
        }

        log.info("短信发送完成，成功：{}条，失败：{}条", successCount, failCount);

        // 如果有失败的短信，记录但不抛出异常，避免影响定时任务的正常执行
        if (failCount > 0) {
            log.warn("有{}条短信发送失败，请检查短信服务状态", failCount);
        }

        // 记录短信服务调用情况
        log.info("短信服务调用统计：");
        log.info("  - 短信服务实例：{}", smsService.getClass().getName());
        log.info("  - 总批次数：{}", splitPhones.size());
        log.info("  - 成功发送：{}条", successCount);
        log.info("  - 发送失败：{}条", failCount);
        log.info("  - 成功率：{}%", splitPhones.size() > 0 ?
            String.format("%.2f", (double) successCount / (successCount + failCount) * 100) : "0.00");
    }

    /**
     * 测试短信服务连接
     * 用于调试短信服务是否正常工作
     */
    private void testSmsService() {
        try {
            log.info("测试短信服务连接...");
            log.info("短信服务实例：{}", smsService != null ? smsService.getClass().getName() : "null");

            if (smsService == null) {
                log.error("短信服务未注入，请检查配置");
                return;
            }

            // 这里可以添加一个简单的测试方法调用
            log.info("短信服务连接正常");

        } catch (Exception e) {
            log.error("短信服务连接测试失败", e);
        }
    }


}
