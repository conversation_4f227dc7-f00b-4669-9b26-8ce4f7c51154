# 定时任务短信发送修复说明

## 问题描述

定时任务执行短信发送时出现Hystrix超时异常，导致任务失败。错误信息显示：
- `HystrixTimeoutException`: DeviceServiceClient#getIotCardByPage方法调用超时
- 任务ID：2，执行失败

## 问题分析

1. **根本原因**: Hystrix默认超时时间过短（通常为1秒），无法满足查询大量物联网卡数据的需求
2. **调用链路**: SmsTask -> DeviceServiceClient.getIotCardByPage() -> 设备管理服务
3. **影响范围**: 定时任务无法正常执行，短信预警功能失效

## 修复方案

### 1. 配置文件修复

#### 新增 `application.yml` 配置
位置：`bto-solarman-system/src/main/resources/application.yml`

```yaml
# Hystrix配置
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 60000  # 默认超时时间60秒
    # 针对DeviceServiceClient的特殊配置
    DeviceServiceClient#getIotCardByPage(IotCardQuery,String):
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 120000  # 2分钟超时

# Feign配置
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 30000  # 连接超时30秒
        readTimeout: 60000     # 读取超时60秒
      solarman-device:
        connectTimeout: 30000
        readTimeout: 120000    # 读取超时2分钟

# Ribbon配置
ribbon:
  ConnectTimeout: 30000      # 连接超时30秒
  ReadTimeout: 60000         # 读取超时60秒
```

### 2. 代码优化

#### SmsTask类重构
- 添加重试机制：查询失败时自动重试3次
- 改进错误处理：区分不同类型的异常并记录详细日志
- 优化批处理：添加批次间延迟，避免过于频繁的请求
- 增强日志记录：详细记录任务执行过程和结果

#### 新增配置类
1. **HystrixConfig**: 专门配置Hystrix熔断器和超时参数
2. **FeignConfig**: 配置Feign客户端超时和重试策略
3. **ScheduleConfig**: 配置定时任务专用线程池

#### DeviceServiceClientFallbackFactory优化
- 改进降级逻辑，提供更详细的错误信息
- 区分超时异常和其他异常类型

### 3. 关键配置参数说明

| 配置项 | 原值 | 新值 | 说明 |
|--------|------|------|------|
| Hystrix超时时间 | 1000ms | 120000ms | 增加到2分钟，满足大数据量查询需求 |
| Feign连接超时 | 默认 | 30000ms | 30秒连接超时 |
| Feign读取超时 | 默认 | 120000ms | 2分钟读取超时 |
| 重试次数 | 0 | 3 | 查询失败时最多重试3次 |
| 批处理延迟 | 无 | 1000ms | 批次间1秒延迟 |

## 部署说明

### 1. 重启服务
修复完成后需要重启 `solarman-system` 服务以使配置生效。

### 2. 验证修复
1. 查看服务启动日志，确认配置加载成功
2. 手动触发定时任务，观察执行情况
3. 检查短信发送日志，确认功能正常

### 3. 监控建议
1. 监控Hystrix指标，关注超时和熔断情况
2. 监控定时任务执行日志，及时发现异常
3. 监控短信发送成功率，确保业务正常

## 预期效果

1. **解决超时问题**: Hystrix超时异常不再出现
2. **提高稳定性**: 增加重试机制，提高任务成功率
3. **改善监控**: 详细的日志记录便于问题排查
4. **优化性能**: 批处理优化减少系统压力

## 注意事项

1. **配置优先级**: application.yml配置会覆盖默认配置
2. **内存使用**: 增加超时时间可能导致线程占用时间延长
3. **网络依赖**: 修复效果依赖于网络稳定性和设备服务响应速度
4. **监控告警**: 建议设置相关监控告警，及时发现问题

## 后续优化建议

1. **异步处理**: 考虑将短信发送改为异步处理，避免阻塞定时任务
2. **缓存优化**: 对频繁查询的数据添加缓存机制
3. **分页查询**: 对大数据量查询实现分页处理
4. **健康检查**: 添加服务健康检查机制
