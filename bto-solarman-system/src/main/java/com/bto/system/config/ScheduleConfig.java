package com.bto.system.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 定时任务配置类
 * 用于配置定时任务执行的线程池和相关参数
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Configuration
@EnableAsync
public class ScheduleConfig {

    /**
     * 配置定时任务专用线程池
     * 用于执行定时任务，避免阻塞主线程池
     */
    @Bean("scheduleTaskExecutor")
    public Executor scheduleTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(5);
        
        // 最大线程数
        executor.setMaxPoolSize(10);
        
        // 队列容量
        executor.setQueueCapacity(200);
        
        // 线程名前缀
        executor.setThreadNamePrefix("schedule-task-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(60);
        
        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        
        log.info("定时任务线程池初始化完成");
        
        return executor;
    }

    /**
     * 配置短信发送专用线程池
     * 用于异步发送短信，提高任务执行效率
     */
    @Bean("smsTaskExecutor")
    public Executor smsTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(3);
        
        // 最大线程数
        executor.setMaxPoolSize(8);
        
        // 队列容量
        executor.setQueueCapacity(100);
        
        // 线程名前缀
        executor.setThreadNamePrefix("sms-task-");
        
        // 线程空闲时间（秒）
        executor.setKeepAliveSeconds(300);
        
        // 拒绝策略：记录日志并丢弃
        executor.setRejectedExecutionHandler((r, executor1) -> {
            log.warn("短信发送任务队列已满，丢弃任务: {}", r.toString());
        });
        
        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间（秒）
        executor.setAwaitTerminationSeconds(30);
        
        executor.initialize();
        
        log.info("短信发送线程池初始化完成");
        
        return executor;
    }
}
