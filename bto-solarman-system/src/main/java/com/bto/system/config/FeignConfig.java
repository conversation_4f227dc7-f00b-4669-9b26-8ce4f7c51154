package com.bto.system.config;

import feign.Logger;
import feign.Request;
import feign.Retryer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Feign配置类
 * 用于配置Feign客户端的超时时间和重试策略
 * 
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Configuration
public class FeignConfig {

    /**
     * 配置Feign请求选项
     * 设置连接超时和读取超时时间
     */
    @Bean
    public Request.Options feignOptions() {
        return new Request.Options(
                30000,  // 连接超时30秒
                120000  // 读取超时2分钟
        );
    }

    /**
     * 配置Feign重试策略
     * 设置重试间隔和最大重试次数
     */
    @Bean
    public Retryer feignRetryer() {
        // 初始间隔1秒，最大间隔3秒，最大重试次数3次
        return new Retryer.Default(1000, 3000, 3);
    }


}
