package com.bto.system.config;

import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.HystrixThreadPoolKey;
import com.netflix.hystrix.HystrixThreadPoolProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

/**
 * Hystrix配置类
 * 用于配置熔断器和超时时间，解决定时任务中Feign调用超时问题
 * 注意：主要配置通过application.yml文件实现，这里提供自定义命令类
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Configuration
public class HystrixConfig {

    /**
     * 自定义Hystrix命令用于设备服务调用
     * 专门处理getIotCardByPage方法的超时问题
     */
    public static class DeviceServiceCommand extends HystrixCommand<Object> {
        
        private final Runnable action;
        
        public DeviceServiceCommand(Runnable action) {
            super(Setter
                    .withGroupKey(HystrixCommandGroupKey.Factory.asKey("DeviceServiceGroup"))
                    .andCommandKey(HystrixCommandKey.Factory.asKey("getIotCardByPage"))
                    .andThreadPoolKey(HystrixThreadPoolKey.Factory.asKey("DeviceServicePool"))
                    .andCommandPropertiesDefaults(
                            HystrixCommandProperties.Setter()
                                    .withExecutionTimeoutInMilliseconds(120000) // 2分钟超时
                                    .withExecutionIsolationStrategy(HystrixCommandProperties.ExecutionIsolationStrategy.THREAD)
                                    .withCircuitBreakerEnabled(true)
                                    .withCircuitBreakerRequestVolumeThreshold(5)
                                    .withCircuitBreakerSleepWindowInMilliseconds(15000)
                                    .withCircuitBreakerErrorThresholdPercentage(70)
                    )
                    .andThreadPoolPropertiesDefaults(
                            HystrixThreadPoolProperties.Setter()
                                    .withCoreSize(10)
                                    .withMaximumSize(20)
                                    .withAllowMaximumSizeToDivergeFromCoreSize(true)
                                    .withMaxQueueSize(100)
                                    .withQueueSizeRejectionThreshold(80)
                    )
            );
            this.action = action;
        }
        
        @Override
        protected Object run() throws Exception {
            action.run();
            return null;
        }
        
        @Override
        protected Object getFallback() {
            log.warn("DeviceService调用失败，执行降级逻辑");
            return null;
        }
    }
}
