# Hystrix配置
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 60000  # 默认超时时间60秒
        timeout:
          enabled: true
      circuitBreaker:
        enabled: true
        requestVolumeThreshold: 20
        sleepWindowInMilliseconds: 5000
        errorThresholdPercentage: 50
    # 针对DeviceServiceClient的特殊配置
    DeviceServiceClient#getIotCardByPage(IotCardQuery,String):
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 120000  # 2分钟超时
        timeout:
          enabled: true
      circuitBreaker:
        enabled: true
        requestVolumeThreshold: 10
        sleepWindowInMilliseconds: 10000
        errorThresholdPercentage: 60
  threadpool:
    default:
      coreSize: 20
      maximumSize: 50
      allowMaximumSizeToDivergeFromCoreSize: true
      maxQueueSize: 200
      queueSizeRejectionThreshold: 180

# Feign配置
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 30000  # 连接超时30秒
        readTimeout: 60000     # 读取超时60秒
        loggerLevel: basic
      # 针对设备服务的特殊配置
      solarman-device:
        connectTimeout: 30000
        readTimeout: 120000    # 读取超时2分钟
        loggerLevel: full

# Ribbon配置
ribbon:
  ConnectTimeout: 30000      # 连接超时30秒
  ReadTimeout: 60000         # 读取超时60秒
  MaxAutoRetries: 1          # 最大重试次数
  MaxAutoRetriesNextServer: 1 # 切换服务器重试次数
  OkToRetryOnAllOperations: false
  
# 日志配置
logging:
  level:
    com.bto.api.feign: DEBUG
    com.netflix.hystrix: DEBUG
    feign: DEBUG
