package com.bto.sms.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendBatchSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendBatchSmsResponse;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendBatchSmsResponseBody;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.bto.api.feign.devicemanage.DeviceServiceClient;
import com.bto.commons.constant.Constant;
import com.bto.commons.constant.SmsApiEnum;
import com.bto.commons.exception.BusinessException;
import com.bto.commons.pojo.dto.ResetPasswdDTO;
import com.bto.commons.pojo.dto.SmsInfoDTO;
import com.bto.commons.pojo.entity.SmsLog;
import com.bto.commons.response.Result;
import com.bto.commons.response.ResultEnum;
import com.bto.redis.utils.RedisUtil;
import com.bto.sms.service.SmsLogService;
import com.bto.sms.service.SmsService;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static com.bto.commons.constant.RedisKey.RESET_PASSWD_CODE;

/**
 * <AUTHOR>
 * @date 2023/11/10 15:13
 */
@Service
@Slf4j
public class SmsServiceImpl implements SmsService {
    @Autowired
    private DeviceServiceClient deviceServiceClient;
    @Autowired
    private SmsLogService smsLogService;
    @Resource
    private RedisUtil redisUtil;


    private static final String ENDPOINT = "dysmsapi.aliyuncs.com";

    @Override
    public Result sendResetCodeMsg(String phoneNumber) {

        // 获取验证码信息
        ResetPasswdDTO resetPasswdDTO;
        resetPasswdDTO = (ResetPasswdDTO) redisUtil.get(RESET_PASSWD_CODE + phoneNumber);

        if (resetPasswdDTO != null && DateUtil.date().isBefore(DateUtil.offsetMinute(resetPasswdDTO.getCreateTime(), 1))) {
            throw new BusinessException(ResultEnum.OPERATION_FAILED.getCode(), "请勿频繁发送验证码");
        }


        // 生成四位数的验证码
        String code = RandomUtil.randomNumbers(4);
        JSONObject codeJson = new JSONObject();
        codeJson.put("code", code);

        // 创建 ResetPasswdDTO 对象并存储到 Redis 中  设置过期时间为 300 秒 (5分钟)
        resetPasswdDTO = new ResetPasswdDTO(code, DateUtil.date());
        redisUtil.set("RESET_PASSWD_CODE:" + phoneNumber, resetPasswdDTO, 300L);


        // ArrayList<String> signNames = new ArrayList<>();
        // signNames.add("博通新能源");
        // String signNamesStr = JSONObject.toJSONString(signNames);
        // String phoneNumsStr = JSONObject.toJSONString(phoneNumber);
        String codeStr = JSONObject.toJSONString(codeJson);

        boolean sendSms = this.sendSms("博通新能源", "SMS_257795488", phoneNumber, codeStr);
        if (sendSms){
            return Result.success(ResultEnum.OPERATION_SUCCESS);
        }
        return Result.failed(ResultEnum.OPERATION_FAILED);
    }

    @Override
    public void sendAlarmMsgByPhone(List<String> phoneNumbers) throws ExecutionException, InterruptedException {
        List<String> cimiList = deviceServiceClient.getCimiListByPhone(phoneNumbers);
        batchSendAlarmMsgByImei(cimiList);
    }

    public boolean sendSms(String signName, String templateCode, String phoneNumbers, String templateParam) {
        // 0 判断手机号
        if (StringUtils.isEmpty(phoneNumbers)) {
            // 处理手机号为空的情况
            return false;
        }
        // 1 创建初始化对象
        DefaultProfile profile =
                DefaultProfile.getProfile("default", Constant.ALIYUN_ACCESS_KEY_ID, Constant.ALIYUN_ACCESS_KEY_SECRET);
        IAcsClient client = new DefaultAcsClient(profile);
        // 2 创建request对象
        CommonRequest request = new CommonRequest();
        // 3 向request里设置参数
        request.setSysVersion("2017-05-25");
        request.setSysDomain(ENDPOINT);
        request.setSysMethod(MethodType.POST);
        request.setSysAction("SendSms");
        request.putQueryParameter("PhoneNumbers", phoneNumbers);
        request.putQueryParameter("SignName", signName);
        request.putQueryParameter("TemplateCode", templateCode);
        request.putQueryParameter("TemplateParam", templateParam);


        try {
            // 4 调用初始化兑现方法实现发送
            CommonResponse response = client.getCommonResponse(request);
            // 5 通过response获取发送是否成功
            boolean result = response.getHttpResponse().isSuccess();

            String data = response.getData();
            JSONObject json = JSONObject.parseObject(data);
            Map<String, String> respBodyMap = json.toJavaObject(Map.class);
            SmsLog smsLog = new SmsLog();
            smsLog.setMobile(phoneNumbers);
            smsLog.setPlatform(0);
            smsLog.setPlatformId(0L);
            smsLog.setParams(templateParam);
            smsLog.setRespBizId(respBodyMap.get("BizId"));
            smsLog.setRespCode(respBodyMap.get("Code"));
            smsLog.setRespMessage(respBodyMap.get("Message"));
            smsLog.setRespRequestId(respBodyMap.get("RequestId"));
            smsLog.setCreateTime(DateUtil.date());

            if (SmsApiEnum.OK.getCode().equals(respBodyMap.get("Code"))) {
                log.info("==================重置密码验证码短信发送成功==================");
                smsLog.setStatus(1);
            } else {
                log.warn("==================重置密码验证码短信发送失败==================");
                log.warn(smsLog.toString());
                smsLog.setStatus(0);
                throw new BusinessException(smsLog.getRespMessage());
            }
            // smsLogService.saveLogs(smsLog);

            return result;
        } catch (Exception e) {
            if (e instanceof BusinessException) {
                throw new BusinessException(e.getMessage());
            }
            throw new BusinessException(ResultEnum.SYSTEM_RUNTIME_FAILED);
        }

    }

    @Override
    public void batchSendAlarmMsgByImei(List<String> cimiList) throws ExecutionException, InterruptedException {
        Result<List<SmsInfoDTO>> result = deviceServiceClient.getSmsInfo(cimiList);
        if (result.getStatus().equals(ResultEnum.SUCCESS.getCode())) {
            List<SmsInfoDTO> smsInfoList = result.getData();
            List<String> cimiCollect = smsInfoList.stream().map(SmsInfoDTO::getUserPhone).collect(Collectors.toList());
            List<List<String>> splitPhones = ListUtil.split(cimiCollect, 100);
            List<List<SmsInfoDTO>> splitSmsInfo = ListUtil.split(smsInfoList, 100);
            for (int i = 0; i < splitPhones.size(); i++) {
                batchSendAlarmMsg(splitPhones.get(i), splitSmsInfo.get(i));
            }
        } else {
            throw new BusinessException(ResultEnum.SMS_NO_CONTENT);
        }
    }

    @Override
    public void batchSendAlarmMsg(List<String> phoneNumbers, List<SmsInfoDTO> smsInfoList) throws ExecutionException, InterruptedException {
        // 配置凭据身份验证信息，包括ak、secret、token
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                // 请确保已设置环境变量ALIBAA_CLOUD_ACCESS_KEY_ID和ALIBAA_CLOUD_ACCESS_KEY_SECRET。
                .accessKeyId(Constant.ALIYUN_ACCESS_KEY_ID)
                .accessKeySecret(Constant.ALIYUN_ACCESS_KEY_SECRET)
                // 使用STS令牌
                //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN"))
                .build());

        // 配置客户端
        AsyncClient client = AsyncClient.builder()
                // 区域 ID
                .region("cn-hangzhou")
                // 使用配置的HttpClient，否则使用默认的HttpClient (Apache HttpClient)
                //.httpClient(httpClient)
                .credentialsProvider(provider)
                // 服务级别配置
                //.serviceConfiguration(Configuration.create())
                // 客户端级配置重写，可以设置Endpoint、Http请求参数等。
                .overrideConfiguration(
                        ClientOverrideConfiguration.create()
                                // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
                                .setEndpointOverride("dysmsapi.aliyuncs.com")
                        //.setConnectTimeout(Duration.ofSeconds(30))
                )
                .build();
        ArrayList<String> signNames = new ArrayList<>();
        phoneNumbers.forEach(phone -> signNames.add("广东博通新能源科技"));
        String signNamesStr = JSONObject.toJSONString(signNames);
        String phoneNumsStr = JSONObject.toJSONString(phoneNumbers);
        String templateParamsStr = JSONObject.toJSONString(smsInfoList);
        SendBatchSmsRequest sendBatchSmsRequest = SendBatchSmsRequest.builder()
                .phoneNumberJson(phoneNumsStr)
                .signNameJson(signNamesStr)
                .templateCode("SMS_492490386")
                .templateParamJson(templateParamsStr)
                // 请求级配置重写，可以设置Http请求参数等。
                // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                .build();
        // 异步获取API请求的返回值
        CompletableFuture<SendBatchSmsResponse> response = client.sendBatchSms(sendBatchSmsRequest);
        // 同步获取API请求的返回值
        SendBatchSmsResponse resp = response.get();
        SendBatchSmsResponseBody respBody = resp.getBody();
        SmsLog smsLog = new SmsLog();
        smsLog.setMobile(phoneNumsStr);
        smsLog.setPlatform(0);
        smsLog.setPlatformId(0L);
        smsLog.setParams(templateParamsStr);
        smsLog.setRespBizId(respBody.getBizId());
        smsLog.setRespCode(respBody.getCode());
        smsLog.setRespMessage(respBody.getMessage());
        smsLog.setRespRequestId(respBody.getRequestId());
        smsLog.setCreateTime(DateUtil.date());

        if (SmsApiEnum.OK.getCode().equals(respBody.getCode())) {
            log.info("==================告警短信发送成功==================");
            smsLog.setStatus(1);
        } else {
            log.info("==================告警短信发送失败==================");
            smsLog.setStatus(0);
        }
        smsLogService.saveLogs(smsLog);
        // 最后，关闭客户端
        client.close();
    }
}
