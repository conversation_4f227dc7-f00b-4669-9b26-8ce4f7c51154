package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.entity.InverterComponent;
import com.bto.commons.pojo.dto.DeviceAndInverterComponentDTO;
import com.bto.commons.pojo.dto.InverterComponentQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/5/25 8:38
 */
@Mapper(componentModel = "spring")
public interface DeviceAndInverterComponentVOMapper {
    DeviceAndInverterComponentVOMapper INSTANCE = Mappers.getMapper(DeviceAndInverterComponentVOMapper.class);

    @Mappings({
            @Mapping(source = "inverterSn",target="deviceId")
    })
    Device deviceAndInverterComponentVO2Device(DeviceAndInverterComponentDTO deviceAndComponentInfoVO);

    InverterComponent deviceAndInverterComponentVO2InverterComponent(DeviceAndInverterComponentDTO deviceAndComponentInfoVO);

    InverterComponentQueryDTO deviceAndInverterComponentVO2InverterComponentQueryVO(DeviceAndInverterComponentDTO deviceAndComponentInfoVO);
}
