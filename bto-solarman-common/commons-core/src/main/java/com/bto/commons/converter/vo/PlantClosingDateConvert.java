package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.PlantClosingDateEntity;
import com.bto.commons.pojo.vo.PlantClosingDateVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 运维结算表
*
* <AUTHOR> 
* @since  2024-06-21
*/
@Mapper
public interface PlantClosingDateConvert {
    PlantClosingDateConvert INSTANCE = Mappers.getMapper(PlantClosingDateConvert.class);

    PlantClosingDateEntity convert(PlantClosingDateVO vo);

    PlantClosingDateVO convert(PlantClosingDateEntity entity);

    List<PlantClosingDateVO> convertList(List<PlantClosingDateEntity> list);

}