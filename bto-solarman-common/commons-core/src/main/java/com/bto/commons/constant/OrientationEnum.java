package com.bto.commons.constant;

/**
 * <AUTHOR>
 * @date 2023/4/19 17:33
 */
public enum OrientationEnum {
    /**
     * 电站 Orientation 属性方向信息
     */
    divide("0","不一致"),
    SAME("1","一致"),
    HERRINGBONE("2","人字形"),
    IN_LINE("3","一字形"),
    NONE("-1","");

    private String code;
    private String name;

    private OrientationEnum(String code, String name){
        this.name=name;
        this.code=code;
    }

    public static String getNameByCode(String code){
        for(OrientationEnum orientationEnum : OrientationEnum.values()){
            if(orientationEnum.getCode().equals(code)){
                return orientationEnum.getName();
            }
        }
        return NONE.getName();
    }

    public static String getCodeByName(String name){
        for(OrientationEnum orientationEnum : OrientationEnum.values()){
            if(orientationEnum.getName().equals(name)){
                return orientationEnum.getCode();
            }
        }
        return NONE.getCode();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}

