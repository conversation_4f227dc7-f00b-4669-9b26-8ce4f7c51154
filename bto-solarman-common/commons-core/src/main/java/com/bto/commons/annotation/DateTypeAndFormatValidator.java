package com.bto.commons.annotation;

import com.bto.commons.pojo.dto.StatisticsQueryDTO;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

/**
 * 日期类型和日期格式联动校验实现
 */
public class DateTypeAndFormatValidator implements ConstraintValidator<ValidDateTypeAndFormat, StatisticsQueryDTO> {

    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$"); // yyyy-MM-dd
    private static final Pattern MONTH_PATTERN = Pattern.compile("^\\d{4}-\\d{2}$"); // yyyy-MM
    private static final Pattern YEAR_PATTERN = Pattern.compile("^\\d{4}$"); // yyyy

    @Override
    public void initialize(ValidDateTypeAndFormat constraintAnnotation) {
        // 初始化校验器
    }

    @Override
    public boolean isValid(StatisticsQueryDTO dto, ConstraintValidatorContext context) {
        if (dto == null || dto.getDateType() == null) {
            return true; // 让基本的@NotNull注解来处理空值
        }

        String dateType = dto.getDateType();
        String date = dto.getDate();

        boolean isValid = true;
        String message = null;

        switch (dateType) {
            case "day":
                isValid = date != null && DATE_PATTERN.matcher(date).matches();
                message = "当日期类型为day时，日期格式必须为yyyy-MM-dd";
                break;
            case "month":
                isValid = date != null && MONTH_PATTERN.matcher(date).matches();
                message = "当日期类型为month时，日期格式必须为yyyy-MM";
                break;
            case "year":
                isValid = date != null && YEAR_PATTERN.matcher(date).matches();
                message = "当日期类型为year时，日期格式必须为yyyy";
                break;
            case "total":
                isValid = date == null;
                message = "当日期类型为total时，日期必须为null";
                break;
            default:
                isValid = false;
                message = "日期类型必须为day、month、year或total";
                break;
        }

        if (!isValid) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(message)
                   .addPropertyNode("date")
                   .addConstraintViolation();
        }

        return isValid;
    }
}