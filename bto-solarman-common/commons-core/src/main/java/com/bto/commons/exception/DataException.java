package com.bto.commons.exception;

import com.bto.commons.response.ResultEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 自定义异常
 * <AUTHOR>
 * @date 2023/5/18 15:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataException extends RuntimeException{
    private static final long serialVersionUID = 1L;

    private String code;
    private String msg;

    public DataException(String msg) {
        super(msg);
        this.code = ResultEnum.REQUIREDPARAM_EMPTY.getCode();
        this.msg = msg;
    }

    public DataException(ResultEnum resultEnum) {
        super(resultEnum.getMessage());
        this.code = resultEnum.getCode();
        this.msg = resultEnum.getMessage();
    }

    public DataException(String msg, Throwable e) {
        super(msg, e);
        this.code = ResultEnum.REQUIREDPARAM_EMPTY.getCode();
        this.msg = msg;
    }
}
