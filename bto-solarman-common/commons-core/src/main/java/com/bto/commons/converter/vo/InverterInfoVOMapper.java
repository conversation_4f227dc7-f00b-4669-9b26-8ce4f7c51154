package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.entity.InverterLatest;
import com.bto.commons.pojo.dto.InverterInfoDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/4/11 15:59
 */
@Mapper(componentModel = "spring")
public interface InverterInfoVOMapper {

    InverterInfoVOMapper INSTANCE = Mappers.getMapper(InverterInfoVOMapper.class);

    //InverterInfoVO视图层类 转 InverterLatest数据库实体类
    @Mappings({
            @Mapping(source = "plantUid",target="plantUid"),
            @Mapping(source = "deviceId",target="inverterSn"),
            @Mapping(source = "inverterStatus",target="inverterStatus"),
            @Mapping(source = "power",target="power"),
            @Mapping(source="todayElectricity",target="todayElectricity"),
            @Mapping(source = "monthElectricity",target="monthElectricity"),
            @Mapping(source = "yearElectricity",target="yearElectricity"),
            @Mapping(source = "totalElectricity",target="totalElectricity"),
            @Mapping(source = "projectSpecial",target="projectSpecial"),
            @Mapping(source = "createTime",target="createTime"),
            @Mapping(source = "updateTime",target="updateTime"),
            @Mapping(target = "state",ignore = true)
    })
    InverterLatest inverterInfoVO2InverterLatest(InverterInfoDTO inverterInfoDTO);


    // InverterLatest数据库实体类 转 InverterInfoVO视图层类
    //inverterlatest2inverterinfoVO
    //todo 把其他属性没有map上的，后续补充上
    @Mappings({
            @Mapping(source = "inverterSn",target="deviceId")
    })
    InverterInfoDTO inverterLatest2InverterInfoVO(InverterLatest inverterLatest);



    //InverterInfoVO视图层类 转 Device数据库实体类
    @Mappings({
            @Mapping(source = "plantUid",target="plantUid"),
            //todo 后续需要优化,目前使用querywrapper添加条件字段后不能修改或者删除，需要clear后重新添加条件，而需要使用inverterinfovo转device类的方法，一般是优先查inverterlatest表，然后根据inverterlatest的字段inverter_sn去查device表的信息，所以使用的是inverterlatest表查出的结果里面的inverter_sn字段作为查询device表的条件
            @Mapping(target="deviceId",ignore=true),
            @Mapping(target = "imei",ignore = true),
            @Mapping(target = "deviceType",ignore = true),
            @Mapping(target = "manufacturer",ignore = true),
            @Mapping(target = "module",ignore = true),
            @Mapping(target = "projectSpecial",ignore = true),
            @Mapping(source = "deviceAddress",target = "deviceAddress"),
            @Mapping(source = "devicePc",target="devicePc"),
            @Mapping(source = "cimi",target="cimi"),
            @Mapping(source = "iccid",target="iccid"),
            @Mapping(source = "startTime",target="startTime"),
            @Mapping(source="endTime",target="endTime"),
            @Mapping(source = "softwareVersion",target="softwareVersion"),
            @Mapping(source = "displayVersion",target="displayVersion"),
            @Mapping(source = "controlVersion",target="controlVersion"),
            @Mapping(source = "isDeleted",target="isDeleted"),
            @Mapping(source = "creator",target="creator"),
            @Mapping(source = "createTime",target="createTime"),
            @Mapping(source = "updateTime",target="updateTime"),
            @Mapping(source = "receiveType",target="receiveType"),

    })
    Device inverterInfoVO2Device(InverterInfoDTO inverterInfoDTO);

}
