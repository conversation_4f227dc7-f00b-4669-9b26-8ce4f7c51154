package com.bto.commons.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 日期类型和日期格式联动校验注解
 */
@Documented
@Constraint(validatedBy = DateTypeAndFormatValidator.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidDateTypeAndFormat {
    
    String message() default "日期类型和日期格式不匹配";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}