package com.bto.commons.converter.vo;

import com.bto.commons.pojo.dto.ElectricityStatisticsExportFileDTO;
import com.bto.commons.pojo.dto.ElectricityStatisticsQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/6/29 16:56
 */
@Mapper(componentModel = "spring")
public interface ElectricityStatisticsExportFileVOMapper {
    ElectricityStatisticsExportFileVOMapper INSTANCE = Mappers.getMapper(ElectricityStatisticsExportFileVOMapper.class);

    ElectricityStatisticsExportFileDTO electricityStatisticsQueryVO2ElectricityStatisticsExportFileVOMapper(ElectricityStatisticsQueryDTO electricityStatisticsQueryDTO);

    ElectricityStatisticsQueryDTO electricityStatisticsExportFileVO2ElectricityStatisticsQueryVO(ElectricityStatisticsExportFileDTO electricityStatisticsExportFileVO);
}
