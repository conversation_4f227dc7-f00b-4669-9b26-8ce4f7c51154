package com.bto.commons.converter.dto.decorator;

import com.bto.commons.constant.OrientationEnum;
import com.bto.commons.constant.PlantStatusEnum;
import com.bto.commons.constant.PlantTypeEnum;
import com.bto.commons.pojo.vo.PlantDigestVO;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.utils.BusinessCalculateUtil;

import java.math.BigDecimal;

/**
 * PlantDTOMapperDecorator是PlantDTOMapper的修饰器，调用PlantDTOMapperDecorator对象类型的转换，并且执行业务代码
 *
 * <AUTHOR>
 * @date 2023/7/3 10:24
 */
@Deprecated
public class PlantDTOMapperDecorator {

    //常量1000
    private static final BigDecimal ONE_THOUSAND = new BigDecimal("1000");
    //常量100
    private static final BigDecimal ONE_HUNDRED = new BigDecimal("100");
    //常量1800
    private static final BigDecimal ONE_THOUSAND_EIGHT_HUNDRED = new BigDecimal("1800");
    //常量0.832
    private static final BigDecimal POINT_EIGHT_THREE_TWO = new BigDecimal("0.832");
    //常量0.000997
    private static final BigDecimal POINT_ZERO_ZERO_ZERO_NINE_NINE_SEVEN = new BigDecimal("0.000997");

    public PlantDigestVO plant2PlantDigestDTODecorator(Plant plant) {
        //获取plant的成员变量值
        BigDecimal power = new BigDecimal(plant.getPower());
        BigDecimal plantCapacity = new BigDecimal(plant.getPlantCapacity());
        BigDecimal todayElectricity = new BigDecimal(plant.getTodayElectricity());
        BigDecimal monthElectricity = new BigDecimal(plant.getMonthElectricity());
        BigDecimal yearElectricity = new BigDecimal(plant.getYearElectricity());
        BigDecimal totalElectricity = new BigDecimal(plant.getTotalElectricity());
        BigDecimal salePrice = new BigDecimal(plant.getSalePrice());
        String plantTypeId = plant.getPlantTypeId();
        String plantStatus = plant.getPlantStatus();
        String orientation = plant.getOrientation();

        //plant转plantDTO
        PlantDigestVO plantDigestVO = new PlantDigestVO();
        //装机容量
        plantDigestVO.setPlantCapacity(plantCapacity.divide(PlantDTOMapperDecorator.ONE_THOUSAND, 2, BigDecimal.ROUND_HALF_UP).toString());
        //工作效率
        plantDigestVO.setEfficiency(power.divide(PlantDTOMapperDecorator.ONE_THOUSAND, 2, BigDecimal.ROUND_HALF_UP).divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP).toString());
        //累计等效植树
        plantDigestVO.setEquivalentTreePlanting(String.valueOf(BusinessCalculateUtil.calculate(String.valueOf(totalElectricity)).getEquivalentTrees()));
        //累计减排二氧化碳C
        plantDigestVO.setCarbonDioxide(String.valueOf(BusinessCalculateUtil.calculate(String.valueOf(totalElectricity)).getCo2Reduction()));
        //今日发电量
        plantDigestVO.setTodayElectricity(todayElectricity.divide(PlantDTOMapperDecorator.ONE_HUNDRED,2,BigDecimal.ROUND_HALF_UP).toString());
        //当月发电量
        plantDigestVO.setMonthElectricity(monthElectricity.divide(PlantDTOMapperDecorator.ONE_HUNDRED,2, BigDecimal.ROUND_HALF_UP).toString());
        //当年发电量
        plantDigestVO.setYearElectricity(yearElectricity.divide(PlantDTOMapperDecorator.ONE_HUNDRED,2,BigDecimal.ROUND_HALF_UP).toString());
        plantDigestVO.setTodayEarning(todayElectricity.multiply(salePrice).divide(PlantDTOMapperDecorator.ONE_HUNDRED,2,BigDecimal.ROUND_HALF_UP).toString());
        plantDigestVO.setTotalEarning(totalElectricity.multiply(salePrice).divide(PlantDTOMapperDecorator.ONE_HUNDRED,2,BigDecimal.ROUND_HALF_UP).toString());
        plantDigestVO.setTotalElectricity(totalElectricity.divide(PlantDTOMapperDecorator.ONE_HUNDRED,2,BigDecimal.ROUND_HALF_UP).toString());
        plantDigestVO.setPlantTypeId(PlantTypeEnum.getNameByCode(plantTypeId));
        plantDigestVO.setPlantStatus(PlantStatusEnum.getNameByCode(plantStatus));
        plantDigestVO.setOrientation(OrientationEnum.getNameByCode(orientation));
        plantDigestVO.setUserName("黄建文");
        return plantDigestVO;
    }

}























