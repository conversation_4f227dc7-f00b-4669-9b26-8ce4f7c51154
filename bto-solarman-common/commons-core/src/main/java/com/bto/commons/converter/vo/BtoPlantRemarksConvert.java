package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.BtoPlantRemarksEntity;
import com.bto.commons.pojo.vo.BtoPlantRemarksVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 电站备注信息
*
* <AUTHOR> 
* @since 1.0.0 2024-06-20
*/
@Mapper
public interface BtoPlantRemarksConvert {
    BtoPlantRemarksConvert INSTANCE = Mappers.getMapper(BtoPlantRemarksConvert.class);

    BtoPlantRemarksEntity convert(BtoPlantRemarksVO vo);

    BtoPlantRemarksVO convert(BtoPlantRemarksEntity entity);

    List<BtoPlantRemarksVO> convertList(List<BtoPlantRemarksEntity> list);

}