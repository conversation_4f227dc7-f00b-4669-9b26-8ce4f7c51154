package com.bto.commons.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2023/12/18 15:59
 */
@Getter
@AllArgsConstructor
public enum PvAbnormalStatusEnum {
    UNPROCESSED(0, "未处理"),
    PROCESSED(1, "已处理"),
    INVALID(2, "失效");

    private final int value;
    private final String name;

    public static String getNameByValue(int value) {
        for (PvAbnormalStatusEnum s : PvAbnormalStatusEnum.values()) {
            if (s.getValue() == value) {
                return s.getName();
            }
        }
        return "";
    }

    public static Integer getValueByName(String name) {
        for (PvAbnormalStatusEnum s : PvAbnormalStatusEnum.values()) {
            if (Objects.equals(s.getName(), name)) {
                return s.getValue();
            }
        }
        return null;
    }
}
