package com.bto.commons.converter.dto;

import com.bto.commons.constant.OrientationEnum;
import com.bto.commons.constant.PlantStatusEnum;
import com.bto.commons.constant.PlantTypeEnum;
import com.bto.commons.constant.PowerDistributorEnum;
import com.bto.commons.pojo.vo.PlantDigestVO;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.utils.BusinessCalculateUtil;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/7/3 10:20
 */
@Mapper(componentModel = "spring")
public interface PlantDTOMapper {

    PlantDTOMapper INSTANCE = Mappers.getMapper(PlantDTOMapper.class);

    PlantDigestVO plant2PlantDTO(Plant plant);

    Plant plantDTO2Plant(PlantDigestVO plantDigestVO);

    /**
     * 常量1000
     */
    static final BigDecimal ONE_THOUSAND = new BigDecimal("1000");
    /**
     * 常量100
     */
    static final BigDecimal ONE_HUNDRED = new BigDecimal("100");
    /**
     * 常量10
     */
    static final BigDecimal TEN = new BigDecimal("10");
    /**
     * 常量1800
     */
    static final BigDecimal ONE_THOUSAND_EIGHT_HUNDRED = new BigDecimal("1800");
    /**
     * 常量0.832
     */
    static final BigDecimal POINT_EIGHT_THREE_TWO = new BigDecimal("0.832");
    /**
     * 常量0.000997
     */
    static final BigDecimal POINT_ZERO_ZERO_ZERO_NINE_NINE_SEVEN = new BigDecimal("0.000997");


    /**
     * plant2PlantDTODecorator是plant2PlantDTO的包装方法，区别在与包装方法里面有逻辑代码，单位换算
     * @param plant
     * @return
     */
    default PlantDigestVO plant2PlantDTODecorator(Plant plant) {
        //获取plant的成员变量值
        BigDecimal power = new BigDecimal(plant.getPower());
        BigDecimal plantCapacity = new BigDecimal(plant.getPlantCapacity());
        BigDecimal todayElectricity = new BigDecimal(plant.getTodayElectricity());
        BigDecimal monthElectricity = new BigDecimal(plant.getMonthElectricity());
        BigDecimal yearElectricity = new BigDecimal(plant.getYearElectricity());
        BigDecimal totalElectricity = new BigDecimal(plant.getTotalElectricity());
        BigDecimal salePrice = new BigDecimal(plant.getSalePrice());
        String plantTypeId = plant.getPlantTypeId();
        String plantStatus = plant.getPlantStatus();
        String orientation = plant.getOrientation();
        String powerDistributor = plant.getPowerDistributor();

        //plant转plantDTO
        PlantDigestVO plantDigestVO = plant2PlantDTO(plant);
        //装机容量
        plantDigestVO.setPlantCapacity(plantCapacity.divide(ONE_THOUSAND, 3, BigDecimal.ROUND_HALF_UP).toString());
        //工作效率
        plantDigestVO.setEfficiency(power.divide(ONE_THOUSAND, 2, BigDecimal.ROUND_HALF_UP).divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP).toString());
        //累计等效植树
        plantDigestVO.setEquivalentTreePlanting(String.valueOf(BusinessCalculateUtil.calculate(String.valueOf(totalElectricity)).getEquivalentTrees()));
        //累计减排二氧化碳
        plantDigestVO.setCarbonDioxide(String.valueOf(BusinessCalculateUtil.calculate(String.valueOf(totalElectricity)).getCo2Reduction()));
        //今日发电量
        plantDigestVO.setTodayElectricity(todayElectricity.divide(ONE_HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString());
        //当月发电量
        plantDigestVO.setMonthElectricity(monthElectricity.divide(ONE_HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString());
        //当年发电量
        plantDigestVO.setYearElectricity(yearElectricity.divide(ONE_HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString());
        //今日收益
        plantDigestVO.setTodayEarning(todayElectricity.multiply(salePrice).divide(ONE_HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString());
        //总收益
        plantDigestVO.setTotalEarning(totalElectricity.multiply(salePrice).divide(ONE_HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString());
        //总发电量
        plantDigestVO.setTotalElectricity(totalElectricity.divide(ONE_HUNDRED, 2, BigDecimal.ROUND_HALF_UP).toString());
        //电站类型
        plantDigestVO.setPlantTypeId(PlantTypeEnum.getNameByCode(plantTypeId));
        //电站状态
        plantDigestVO.setPlantStatus(PlantStatusEnum.getNameByCode(plantStatus));
        //电站方向
        plantDigestVO.setOrientation(OrientationEnum.getNameByCode(orientation));
        //电站的配电箱状态
        plantDigestVO.setPowerDistributor(PowerDistributorEnum.getNameByCode(powerDistributor));
        //每小时日效能
        plantDigestVO.setDailyEfficiencyPerHour(todayElectricity.multiply(TEN).divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP).toString());
        //每小时年效能
        plantDigestVO.setYearlyEfficiencyPerHour(yearElectricity.multiply(TEN).divide(plantCapacity, 2, BigDecimal.ROUND_HALF_UP).toString());
        // 电表编号
        plantDigestVO.setMeterId(plant.getMeterId());
        // 进件编号
        plantDigestVO.setOrderId(plant.getOrderId());
        // 合同编号
        plantDigestVO.setContractId(plant.getContractId());
        // 创建时间
        plantDigestVO.setCreateTime(plant.getCreateTime().substring(0,10));
        // 电价
        plantDigestVO.setSalePrice(salePrice.toString());
        // 项目类型
        plantDigestVO.setProjectSpecial(plant.getProjectSpecial());
        // 项目公司
        plantDigestVO.setPlantCompany(plant.getPlantCompany());
        return plantDigestVO;
    }
}
