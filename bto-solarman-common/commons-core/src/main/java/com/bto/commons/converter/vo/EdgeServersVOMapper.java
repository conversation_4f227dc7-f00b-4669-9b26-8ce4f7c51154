package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.entity.EdgeServerRealtime;
import com.bto.commons.pojo.entity.Plant;
import com.bto.commons.pojo.dto.EdgeServerDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/4/27 14:31
 */

@Mapper(componentModel = "spring")
public interface EdgeServersVOMapper {
    EdgeServersVOMapper INSTANCE = Mappers.getMapper(EdgeServersVOMapper.class);

    EdgeServerRealtime edgeServersVO2EdgeServerRealTime(EdgeServerDTO edgeServerDTO);

    @Mappings({
            @Mapping(source = "plantUid",target = "plantUid")
    })
    Plant edgeServersVO2Plant(EdgeServerDTO edgeServerDTO);

    @Mappings({
            @Mapping(source = "status",target="status"),
            @Mapping(source = "enable",target="enable")
    })
    Device edgeServersVO2Device(EdgeServerDTO edgeServerDTO);

}
