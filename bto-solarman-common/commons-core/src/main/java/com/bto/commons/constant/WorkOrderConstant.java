package com.bto.commons.constant;

import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @since 2023/12/19 10:10
 */

public interface WorkOrderConstant {

    @Value("${work-order.prefix-url}")
    String WORK_ORDER_PREFIX_URL = "http://192.168.30.100:8080/";
    // String WORK_ORDER_PREFIX_URL = "https://workorder.btosolarman.com/workorder/";
    String WORK_ORDER_AUTHORIZATION = "AUTHORIZATION";

    String WORK_ORDER_ACCESS_TOKEN_URL = "sys/api/auth/getAccessToken";
    String WORK_ORG_ID_WITH_USER_URL ="sys/api/workorder/org/getOrgIdWithRepairUserByProjectSpecial";
    String WORK_ORDER_GET_SUB_ORG_ID_LIST_URL ="sys/api/workorder/org/getSubOrgIdList";
    String WORK_ORDER_FLOW_URL ="workorder/flow";
    String WORK_ORDER_TOKEN_KEY ="work_order_token";
}
