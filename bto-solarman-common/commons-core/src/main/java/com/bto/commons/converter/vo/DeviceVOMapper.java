package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.Device;
import com.bto.commons.pojo.dto.DeviceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/4/11 8:24
 */
@Mapper(componentModel = "spring")
public interface DeviceVOMapper {
    DeviceVOMapper INSTANCE = Mappers.getMapper(DeviceVOMapper.class);

    @Mapping(target = "startTime",ignore = true)
    @Mapping(target = "endTime",ignore = true)
    @Mapping(target = "createTime",ignore = true)
    @Mapping(target = "updateTime",ignore = true)
    DeviceDTO device2DeviceVO(Device device);

    Device deviceVO2Device(DeviceDTO deviceDTO);
}
