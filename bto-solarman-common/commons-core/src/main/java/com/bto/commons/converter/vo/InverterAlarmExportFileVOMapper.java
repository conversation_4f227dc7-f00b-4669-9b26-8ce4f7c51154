package com.bto.commons.converter.vo;

import com.bto.commons.pojo.dto.InverterAlarmExportFileDTO;
import com.bto.commons.pojo.dto.InverterAlarmQueryDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/6/29 16:20
 */
@Mapper(componentModel = "spring")
public interface InverterAlarmExportFileVOMapper {
    InverterAlarmExportFileVOMapper INSTANCE = Mappers.getMapper(InverterAlarmExportFileVOMapper.class);

    @Mappings({
            @Mapping(source ="pageSize",target="pageSize"),
            @Mapping(source = "currentPage",target="currentPage")
    })
    InverterAlarmExportFileDTO inverterAlarmQueryVO2InverterAlarmExportFileVO(InverterAlarmQueryDTO inverterAlarmQueryDTO);

    @Mappings({
            @Mapping(source ="pageSize",target="pageSize"),
            @Mapping(source = "currentPage",target="currentPage")
    })
    InverterAlarmQueryDTO inverterAlarmExportFileVO2InverterAlarmQueryVo(InverterAlarmExportFileDTO inverterAlarmExportFileVO);

}
