package com.bto.commons.converter.vo;

import com.bto.commons.pojo.vo.ElectricityInfoVO;
import com.bto.commons.pojo.vo.ElectricityInfoWithPlantNameAndUidVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/6/30 9:39
 */
@Mapper(componentModel = "spring")
public interface ElectricityInfoDTOMapper {
    ElectricityInfoDTOMapper INSTANCE = Mappers.getMapper(ElectricityInfoDTOMapper.class);

    ElectricityInfoVO ElectricityInfoWithPlantNameAndUidDTO2ElectricityInfoDTO(ElectricityInfoWithPlantNameAndUidVO electricityInfoWithPlantNameAndUidVO);

    ElectricityInfoWithPlantNameAndUidVO electricityInfoDTO2ElectricityInfoWithPlantNameAndUidDTO(ElectricityInfoVO electricityInfoVO);
}
