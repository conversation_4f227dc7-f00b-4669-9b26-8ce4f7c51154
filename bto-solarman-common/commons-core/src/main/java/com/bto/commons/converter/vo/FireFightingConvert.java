package com.bto.commons.converter.vo;

import com.bto.commons.pojo.entity.FireFightingEntity;
import com.bto.commons.pojo.vo.FireFightingVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface FireFightingConvert {
    FireFightingConvert INSTANCE = Mappers.getMapper(FireFightingConvert.class);

    FireFightingEntity convert(FireFightingVO vo);

    FireFightingVO convert(FireFightingEntity entity);

    List<FireFightingVO> convertList(List<FireFightingEntity> list);

}