package com.bto.commons.constant;

import lombok.Getter;

/**
 * <AUTHOR> by zhb on 2024/11/8.
 */

@Getter
public class LotCardConstant {

    /**
     * API_ID
     */
    public final static String API_ID = "8885633237568358";

    /**
     * API_SECRET
     */
    public final static String API_SECRET = "cLasUqPb8o4SsImG6Yei1J6PefkGbtPy";

    /**
     * 基础URL
     */
    public static final String BASE_URL = "https://iot.commchina.net/api/customer/v1/";

    /**
     * 批量卡号信息查询
     */
    public static final String CARD_INFO_URL = "sim_cards/get_sim_card_detail";

    /**
     * 查询某卡号月使用流量
     */
    public static final String CARD_MONTH_FLOW_URL = "sim_cards/get_flow_month";

    /**
     * 查询某卡号日使用流量
     */
    public static final String CARD_DAY_FLOW_URL = "sim_cards/get_flow_day";

    /**
     * 所有流量池信息
     */
    public static final String ALL_POOL_URL = "traffic_pools/pool_list";

    /**
     * APP_KEY
     */
    public final static String APP_KEY = "LseGczpypmzuqjR5Kw467Brmj8ss8VTp";

    /**
     * APP_SECRET
     */
    public final static String APP_SECRET = "93e95279c08b4f02a5f6c888d8b60bcb";

    /**
     * 基础URL
     */
    public static final String OLD_BASE_URL = "http://www.szlingchuan.com/";

    /**
     * 查询某卡号月使用流量
     */
    public static final String OLD_CARD_MONTH_FLOW_URL = "api/v2/card/getCardsInfo";

    /**
     * 查询卡片数量
     */
    public static final String OLD_CARD_NUMBER_URL = "api/v2/outputApi/queryCardNumber";

    /**
     * 查询卡片数量
     */
    public static final String OLD_CARD_INFO_URL = "api/v2/card/getAllCardInfo";

    /**
     * 查询月流量信息
     */
    public static final String OLD_CARD_USAGE_URL = "api/v2/card/getCardMonthUsage";


}