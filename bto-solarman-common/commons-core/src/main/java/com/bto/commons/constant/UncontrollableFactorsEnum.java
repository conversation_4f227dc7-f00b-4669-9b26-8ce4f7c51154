package com.bto.commons.constant;

public enum UncontrollableFactorsEnum {
    MASTER_ISLANDING_ERROR("Master Islanding Error"),
    ISLANDING_ERROR("Islanding Error"),
    MASTER_PHASE1_FREQUENCY_HIGH("Master Phase1 Frequency High"),
    MASTER_PHASE1_FREQUENCY_LOW("Master Phase1 Frequency Low"),
    MASTER_PHASE1_NO_GRID_ERROR("Master Phase1 No Grid Error"),
    MASTER_PHASE2_NO_GRID_ERROR("Master Phase2 No Grid Error"),
    MASTER_PHASE3_NO_GRID_ERROR("Master Phase3 No Grid Error"),
    SLAVE_PHASE1_FREQUENCY_HIGH("Slave Phase1 Frequency High"),
    SLAVE_PHASE1_VOLTAGE_CONSIS_ERROR("Slave Phase1 Voltage Consis Error"),
    SLAVE_PHASE1_NO_GRID_ERROR("Slave Phase1 No Grid Error"),
    SLAVE_PHASE2_NO_GRID_ERROR("Slave Phase2 No Grid Error"),
    SLAVE_PHASE3_NO_GRID_ERROR("Slave Phase3 No Grid Error"),
    NO_GRID("NO-Grid");

    private final String alarmInfo;

    UncontrollableFactorsEnum(String alarmInfo) {
        this.alarmInfo = alarmInfo;
    }

    public String getAlarmInfo() {
        return alarmInfo;
    }
}