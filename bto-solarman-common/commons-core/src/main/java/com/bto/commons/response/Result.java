package com.bto.commons.response;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一返回结果类
 *
 * <AUTHOR>
 * @date 2023/3/29 10:37
 */
@Data
@NoArgsConstructor
public class Result<T> {
    /**
     * 返回结果状态码
     */
    private String status;

    /**
     * 返回结果状态信息
     */
    private String message;

    /**
     * 返回数据结果
     */
    private T data;

    public Result(String code, String message, T data) {
        this.status = code;
        this.message = message;
        this.data = data;
    }


    /**
     * 返回成功结果
     *
     * @param data 返回数据
     * @return Result 成功结果类
     */
    public static <T> Result<T> success(T data) {
        return new Result<T>(ResultEnum.SUCCESS.getCode(), ResultEnum.SUCCESS.getMessage(), data);
    }

    public static  Result success(ResultEnum resultEnum) {
        return new Result(ResultEnum.SUCCESS.getCode(), ResultEnum.SUCCESS.getMessage(), "");
    }

    public Result(ResultEnum resultEnum, T data) {
        this.status = resultEnum.getCode();
        this.message = resultEnum.getMessage();
        this.data = data;
    }
    public Result(String status, String message) {
        this.status = status;
        this.message = message;
    }
    public static Result success() {
        return new Result(ResultEnum.SUCCESS.getCode(), ResultEnum.OPERATION_SUCCESS.getMessage());
    }
    /**
     * 返回成功结果
     *
     * @param message 状态信息
     * @param data    返回数据
     * @return Result 成功结果类
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<T>(ResultEnum.SUCCESS.getCode(), message, data);
    }

    /**
     * 返回成功结果
     * @param resultEnum
     * @param data
     * @return
     * @param <T>
     */
    public static <T> Result<T> success(ResultEnum resultEnum, T data) {
        return new Result<T>(resultEnum.getCode(), resultEnum.getMessage(), data);
    }

    
    /**
     * 返回失败结果
     *
     * @return Result 失败结果类
     */
    public static Result<?> failed() {
        return new Result<>(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), ResultEnum.SYSTEM_RUNTIME_FAILED.getMessage(), null);
    }

    /**
     * 返回失败结果
     *
     * @param message 异常信息
     * @return Result 失败结果类
     */
    public static Result<?> failed(String message) {
        Result<Object> objectResult = new Result<>(ResultEnum.SYSTEM_RUNTIME_FAILED.getCode(), message, null);
        return objectResult;
    }

    /**
     * 返回失败结果
     *
     * @param errorResult 返回统一数据结果的接口
     * @return Result 失败结果类
     */
    public static Result<?> failed(IResult errorResult) {
        return new Result<>(errorResult.getCode(), errorResult.getMessage(), null);
    }


    /**
     * 返回失败结果
     *
     * @param code    状态码
     * @param message 状态信息
     * @return Result 失败结果类
     */
    public static Result<?> failed(String code, String message) {
        return new Result<>(code, message, null);
    }

    /**
     * 返回结果
     *
     * @param code    状态码
     * @param message 状态信息
     * @param data    数据
     * @return Result 结果类
     */
    public static <T> Result<T> instance(String code, String message, T data) {
        Result<T> result = new Result<>();
        result.setStatus(code);
        result.setMessage(message);
        result.setData(data);
        return result;
    }
    /**
     * 返回结果
     *
     * @param resultEnum    结果枚举
     * @return Result 结果类
     */
    public static  Result instance(ResultEnum resultEnum) {
        return new Result<>(resultEnum,"");
    }
    /**
     * 返回结果
     * @param data    数据
     * @return Result 结果类
     */
    public static <T> Result<T> instance(ResultEnum resultEnum, T data) {
        Result<T> result = new Result<>();
        result.setStatus(resultEnum.getCode());
        result.setMessage(resultEnum.getMessage());
        result.setData(data);
        return result;
    }

    public <T> T getData(TypeReference<T> typeReference) {
        return JSON.parseObject(JSON.toJSONString(data), typeReference);
    }
}
